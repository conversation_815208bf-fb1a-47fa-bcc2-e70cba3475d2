package org.sounfury.aki.application.task.strategy;

import reactor.core.publisher.Flux;

/**
 * 任务策略接口
 * 定义无状态任务的处理策略
 */
public interface TaskStrategy {
    
    /**
     * 执行任务
     * @param request 任务请求
     * @return 任务结果
     */
    TaskResult execute(TaskRequest request);
    
    /**
     * 执行流式任务
     * @param request 任务请求
     * @return 流式任务结果
     */
    Flux<String> executeStream(TaskRequest request);
    
    /**
     * 获取策略名称
     * @return 策略名称
     */
    String getStrategyName();
    
    /**
     * 任务请求
     */
    record TaskRequest(
            String userInput,
            String userName,
            boolean userLoggedIn,
            String characterId,
            String actionName
    ) {}
    
    /**
     * 任务结果
     */
    record TaskResult(
            String response,
            boolean success,
            String errorMessage,
            String strategyName,
            String actionName
    ) {
        public static TaskResult success(String response, String strategyName, String actionName) {
            return new TaskResult(response, true, null, strategyName, actionName);
        }
        
        public static TaskResult failure(String errorMessage, String strategyName, String actionName) {
            return new TaskResult(null, false, errorMessage, strategyName, actionName);
        }
    }
}
