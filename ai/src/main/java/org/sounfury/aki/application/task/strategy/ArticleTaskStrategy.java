package org.sounfury.aki.application.task.strategy;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.sounfury.aki.application.task.dto.ArticleTaskRequest;
import org.sounfury.aki.application.task.dto.BaseTaskRequest;
import org.sounfury.aki.domain.llm.service.ChatClientManager;
import org.sounfury.aki.domain.prompt.template.TemplateType;
import org.sounfury.aki.domain.prompt.template.service.SystemPromptManager;
import org.sounfury.aki.domain.service.CharacterPromptManager;
import org.sounfury.aki.infrastructure.remote.BlogService;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.messages.SystemMessage;
import org.springframework.ai.chat.messages.UserMessage;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.StringJoiner;

/**
 * 文章任务策略
 * 处理文章总结、文章摘录等任务
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ArticleTaskStrategy implements TaskStrategy {
    
    private final ChatClientManager chatClientManager;
    private final SystemPromptManager systemPromptManager;
    private final CharacterPromptManager characterPromptManager;
    private final BlogService blogService;
    
    @Override
    public TaskResult execute(BaseTaskRequest request) {
        if (!(request instanceof ArticleTaskRequest articleRequest)) {
            return TaskResult.failure("请求类型错误，期望ArticleTaskRequest", "ArticleTaskStrategy");
        }
        
        LocalDateTime startTime = LocalDateTime.now();
        log.info("开始执行文章任务，任务模式: {}, 文章ID: {}", 
                articleRequest.getTaskMode(), articleRequest.getArticleId());
        
        try {
            // 1. 获取文章内容
            String articleContent = blogService.getArticle(articleRequest.getArticleId());
            if (articleContent == null || articleContent.trim().isEmpty()) {
                return TaskResult.failure("无法获取文章内容", "ArticleTaskStrategy");
            }
            
            // 2. 构建系统提示词
            String systemPrompt = buildSystemPrompt(articleRequest);
            
            // 3. 构建消息列表
            List<Message> messages = buildMessages(articleContent, systemPrompt);
            
            // 4. 创建Prompt
            Prompt prompt = new Prompt(messages);
            
            // 5. 获取ChatClient并调用
            ChatClient chatClient = chatClientManager.getBasicChatClient();
            ChatResponse chatResponse = chatClient.prompt(prompt).call().chatResponse();
            String response = chatResponse.getResult().getOutput().getText();
            
            log.info("文章任务执行完成，任务模式: {}, 耗时: {}ms", 
                    articleRequest.getTaskMode(),
                    java.time.Duration.between(startTime, LocalDateTime.now()).toMillis());
            
            return TaskResult.success(response, "ArticleTaskStrategy");
            
        } catch (Exception e) {
            log.error("文章任务执行失败，任务模式: {}", articleRequest.getTaskMode(), e);
            return TaskResult.failure(e.getMessage(), "ArticleTaskStrategy");
        }
    }
    
    @Override
    public Flux<String> executeStream(BaseTaskRequest request) {
        if (!(request instanceof ArticleTaskRequest articleRequest)) {
            return Flux.error(new IllegalArgumentException("请求类型错误，期望ArticleTaskRequest"));
        }
        
        log.info("开始执行流式文章任务，任务模式: {}, 文章ID: {}", 
                articleRequest.getTaskMode(), articleRequest.getArticleId());
        
        try {
            // 1. 获取文章内容
            String articleContent = blogService.getArticle(articleRequest.getArticleId());
            if (articleContent == null || articleContent.trim().isEmpty()) {
                return Flux.error(new RuntimeException("无法获取文章内容"));
            }
            
            // 2. 构建系统提示词
            String systemPrompt = buildSystemPrompt(articleRequest);
            
            // 3. 构建消息列表
            List<Message> messages = buildMessages(articleContent, systemPrompt);
            
            // 4. 创建Prompt
            Prompt prompt = new Prompt(messages);
            
            // 5. 获取ChatClient并调用流式接口
            ChatClient chatClient = chatClientManager.getBasicChatClient();
            
            return chatClient
                    .prompt(prompt)
                    .stream()
                    .content()
                    .doOnComplete(() -> {
                        log.info("流式文章任务执行完成，任务模式: {}", articleRequest.getTaskMode());
                    })
                    .doOnError(error -> {
                        log.error("流式文章任务执行失败，任务模式: {}", articleRequest.getTaskMode(), error);
                    });
            
        } catch (Exception e) {
            log.error("流式文章任务执行异常，任务模式: {}", articleRequest.getTaskMode(), e);
            return Flux.error(e);
        }
    }
    
    /**
     * 构建系统提示词
     */
    private String buildSystemPrompt(ArticleTaskRequest request) {
        StringJoiner promptBuilder = new StringJoiner("\n\n");
        
        // 1. 基础系统提示词
        String basePrompt = systemPromptManager.buildBaseSystemPrompt();
        if (!basePrompt.isEmpty()) {
            promptBuilder.add(basePrompt);
        }
        
        // 2. 角色人格提示词（使用默认角色）
        String characterPrompt = characterPromptManager.buildCompleteCharacterPrompt("default");
        if (!characterPrompt.isEmpty()) {
            promptBuilder.add(characterPrompt);
        }
        
        // 3. 用户称呼提示词
        String userPrompt = systemPromptManager.buildUserAddressPrompt(request.getUserName());
        if (!userPrompt.isEmpty()) {
            promptBuilder.add(userPrompt);
        }
        
        // 4. 任务行为指导
        String behaviorPrompt = systemPromptManager.buildBehaviorGuidePrompt(TemplateType.ACTION_BEHAVIOR);
        if (!behaviorPrompt.isEmpty()) {
            promptBuilder.add(behaviorPrompt);
        }
        
        // 5. 任务特定提示词（根据任务模式获取对应模板）
        String taskPrompt = systemPromptManager.buildTaskSpecificPrompt(request.getTaskMode().getCode());
        if (!taskPrompt.isEmpty()) {
            promptBuilder.add(taskPrompt);
        }
        
        return promptBuilder.toString();
    }
    
    /**
     * 构建消息列表
     */
    private List<Message> buildMessages(String articleContent, String systemPrompt) {
        List<Message> messages = new ArrayList<>();
        
        // 1. 添加系统消息
        if (!systemPrompt.isEmpty()) {
            messages.add(new SystemMessage(systemPrompt));
        }
        
        // 2. 添加文章内容作为用户消息
        messages.add(new UserMessage(articleContent));
        
        return messages;
    }
}
