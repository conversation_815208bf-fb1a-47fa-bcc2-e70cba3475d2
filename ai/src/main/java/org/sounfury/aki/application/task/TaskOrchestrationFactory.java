package org.sounfury.aki.application.task;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.sounfury.aki.application.task.strategy.ActionTaskStrategy;
import org.sounfury.aki.application.task.strategy.TaskStrategy;
import org.sounfury.aki.application.task.TaskMode;
import org.springframework.stereotype.Component;

/**
 * 任务编排工厂
 * 根据任务模式创建对应的任务策略
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class TaskOrchestrationFactory {
    
    private final ActionTaskStrategy actionTaskStrategy;
    
    /**
     * 根据任务模式获取任务策略
     * @param mode 任务模式
     * @return 任务策略
     */
    public TaskStrategy getStrategy(TaskMode mode) {
        if (mode == null) {
            log.warn("任务模式为空，使用默认动作策略");
            return actionTaskStrategy;
        }
        
        TaskStrategy strategy = switch (mode) {
            case ACTION -> actionTaskStrategy;
        };
        
        log.debug("获取任务策略: 模式={}, 策略={}", mode.getName(), strategy.getStrategyName());
        return strategy;
    }
    
    /**
     * 检查模式是否支持
     * @param mode 任务模式
     * @return 是否支持
     */
    public boolean isSupported(TaskMode mode) {
        return mode != null && mode == TaskMode.ACTION;
    }
}
