package org.sounfury.aki.application.task.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.sounfury.aki.application.task.TaskMode;
import org.sounfury.aki.application.task.TaskOrchestrationFactory;
import org.sounfury.aki.application.task.strategy.TaskStrategy;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;

/**
 * 任务应用服务
 * 统一处理无状态任务功能，被api层直接调用
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TaskApplicationService {
    
    private final TaskOrchestrationFactory orchestrationFactory;
    
    /**
     * 执行任务
     * @param request 任务请求
     * @return 任务响应
     */
    public TaskResponse executeTask(TaskRequest request) {
        try {
            log.info("处理任务请求，动作: {}, 用户: {}, 角色: {}", 
                    request.getActionName(), request.getUserName(), request.getCharacterId());
            
            // 执行任务编排逻辑
            var result = orchestrateTask(request);
            
            if (result.success()) {
                log.info("任务处理成功，动作: {}", request.getActionName());
                
                return TaskResponse.success(
                        result.response(),
                        result.actionName(),
                        result.characterId(),
                        result.mode(),
                        result.strategyName()
                );
            } else {
                log.error("任务处理失败，动作: {}, 错误: {}", 
                        request.getActionName(), result.errorMessage());
                return TaskResponse.failure(result.errorMessage(), request.getActionName());
            }
            
        } catch (Exception e) {
            log.error("任务处理异常，动作: {}", request.getActionName(), e);
            return TaskResponse.failure("服务异常: " + e.getMessage(), request.getActionName());
        }
    }
    
    /**
     * 执行流式任务
     * @param request 任务请求
     * @return 流式任务响应
     */
    public Flux<String> executeTaskStream(TaskRequest request) {
        try {
            log.info("处理流式任务请求，动作: {}, 用户: {}, 角色: {}", 
                    request.getActionName(), request.getUserName(), request.getCharacterId());
            
            // 执行流式任务编排逻辑
            return orchestrateTaskStream(request)
                    .doOnComplete(() -> {
                        log.info("流式任务完成，动作: {}", request.getActionName());
                    })
                    .doOnError(error -> {
                        log.error("流式任务处理失败，动作: {}", request.getActionName(), error);
                    });
            
        } catch (Exception e) {
            log.error("流式任务处理异常，动作: {}", request.getActionName(), e);
            return Flux.error(e);
        }
    }
    
    /**
     * 解析任务模式
     */
    private TaskMode parseTaskMode(String mode) {
        if (mode == null || mode.trim().isEmpty()) {
            return TaskMode.ACTION;
        }
        
        return switch (mode.toLowerCase()) {
            case "action" -> TaskMode.ACTION;
            default -> TaskMode.ACTION;
        };
    }
    
    /**
     * 执行任务编排
     * @param request 任务请求
     * @return 任务编排结果
     */
    private TaskOrchestrationResult orchestrateTask(TaskRequest request) {
        log.info("开始执行任务编排，动作: {}, 角色: {}", 
                request.getActionName(), request.getCharacterId());
        
        try {
            // 1. 解析任务模式
            TaskMode mode = parseTaskMode(request.getMode());
            
            // 2. 获取任务策略
            TaskStrategy strategy = orchestrationFactory.getStrategy(mode);
            
            // 3. 构建策略请求
            TaskStrategy.TaskRequest strategyRequest = 
                    new TaskStrategy.TaskRequest(
                            request.getUserInput(),
                            request.getUserName(),
                            Boolean.TRUE.equals(request.getIsOwner()),
                            request.getCharacterId(),
                            request.getActionName()
                    );
            
            // 4. 执行策略
            TaskStrategy.TaskResult result = strategy.execute(strategyRequest);
            
            log.info("任务编排执行完成，动作: {}, 成功: {}", 
                    request.getActionName(), result.success());
            
            return TaskOrchestrationResult.from(result, request, mode);
            
        } catch (Exception e) {
            log.error("任务编排执行失败，动作: {}", request.getActionName(), e);
            return TaskOrchestrationResult.failure(
                    e.getMessage(), 
                    request.getActionName(), 
                    "ACTION"
            );
        }
    }
    
    /**
     * 执行流式任务编排
     * @param request 任务请求
     * @return 流式任务结果
     */
    private Flux<String> orchestrateTaskStream(TaskRequest request) {
        log.info("开始执行流式任务编排，动作: {}, 角色: {}", 
                request.getActionName(), request.getCharacterId());
        
        try {
            // 1. 解析任务模式
            TaskMode mode = parseTaskMode(request.getMode());
            
            // 2. 获取任务策略
            TaskStrategy strategy = orchestrationFactory.getStrategy(mode);
            
            // 3. 构建策略请求
            TaskStrategy.TaskRequest strategyRequest = 
                    new TaskStrategy.TaskRequest(
                            request.getUserInput(),
                            request.getUserName(),
                            Boolean.TRUE.equals(request.getIsOwner()),
                            request.getCharacterId(),
                            request.getActionName()
                    );
            
            // 4. 执行流式策略
            return strategy.executeStream(strategyRequest)
                    .doOnComplete(() -> {
                        log.info("流式任务编排执行完成，动作: {}", request.getActionName());
                    })
                    .doOnError(error -> {
                        log.error("流式任务编排执行失败，动作: {}", request.getActionName(), error);
                    });
            
        } catch (Exception e) {
            log.error("流式任务编排执行异常，动作: {}", request.getActionName(), e);
            return Flux.error(e);
        }
    }
    
    /**
     * 任务请求DTO
     */
    @lombok.Builder
    @lombok.Data
    public static class TaskRequest {
        private String userInput;
        private String userName;
        private Boolean isOwner;
        private String characterId;
        private String actionName;
        private String mode;
    }
    
    /**
     * 任务响应DTO
     */
    @lombok.Builder
    @lombok.Data
    public static class TaskResponse {
        private String response;
        private Boolean success;
        private String errorMessage;
        private String actionName;
        private String characterId;
        private String mode;
        private String strategyName;
        
        public static TaskResponse success(String response, String actionName, 
                String characterId, String mode, String strategyName) {
            return TaskResponse.builder()
                    .response(response)
                    .success(true)
                    .actionName(actionName)
                    .characterId(characterId)
                    .mode(mode)
                    .strategyName(strategyName)
                    .build();
        }
        
        public static TaskResponse failure(String errorMessage, String actionName) {
            return TaskResponse.builder()
                    .success(false)
                    .errorMessage(errorMessage)
                    .actionName(actionName)
                    .build();
        }
    }
    
    /**
     * 任务编排结果
     */
    public record TaskOrchestrationResult(
            String response,
            boolean success,
            String errorMessage,
            String strategyName,
            String actionName,
            String mode,
            String characterId
    ) {
        public static TaskOrchestrationResult from(
                TaskStrategy.TaskResult result, 
                TaskRequest request,
                TaskMode mode) {
            return new TaskOrchestrationResult(
                    result.response(),
                    result.success(),
                    result.errorMessage(),
                    result.strategyName(),
                    result.actionName(),
                    mode.getName(),
                    request.getCharacterId()
            );
        }
        
        public static TaskOrchestrationResult failure(
                String errorMessage, 
                String actionName, 
                String mode) {
            return new TaskOrchestrationResult(
                    null,
                    false,
                    errorMessage,
                    "task-application-service",
                    actionName,
                    mode,
                    null
            );
        }
    }
}
