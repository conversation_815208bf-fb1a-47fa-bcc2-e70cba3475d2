package org.sounfury.aki.application.task;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.sounfury.aki.domain.character.CharacterRepository;
import org.sounfury.aki.domain.llm.service.ChatClientManager;

import org.sounfury.aki.domain.prompt.template.TemplateType;
import org.sounfury.aki.domain.prompt.template.service.SystemPromptManager;
import org.sounfury.aki.domain.service.CharacterPromptManager;
import org.sounfury.aki.infrastructure.remote.BlogService;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.messages.SystemMessage;
import org.springframework.ai.chat.messages.UserMessage;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.StringJoiner;

/**
 * 任务应用服务
 * 统一处理无状态任务功能，被api层直接调用
 * 根据动作名动态获取对应的提示词模板
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TaskApplicationService {
    
    private final ChatClientManager chatClientManager;
    private final CharacterRepository characterRepository;
    private final SystemPromptManager systemPromptManager;
    private final CharacterPromptManager characterPromptManager;
    private final BlogService blogService;
    
    /**
     * 执行任务
     * @param request 任务请求
     * @return 任务响应
     */
    public TaskResponse executeTask(TaskRequest request) {
        LocalDateTime startTime = LocalDateTime.now();
        log.info("开始执行动作任务，动作: {}, 角色: {}", request.getActionName(), request.getCharacterId());
        
        try {
            // 1. 获取用户输入内容
            String userInput = getUserInput(request);
            if (userInput == null || userInput.trim().isEmpty()) {
                return TaskResponse.failure("无法获取输入内容", request.getActionName());
            }

            // 2. 构建系统提示词
            String systemPrompt = buildSystemPrompt(request);

            // 3. 构建消息列表
            List<Message> messages = buildMessages(userInput, systemPrompt);
            
            // 4. 创建Prompt
            Prompt prompt = new Prompt(messages);

            // 5. 获取ChatClient并调用（不使用工具）
            ChatClient chatClient = chatClientManager.getBasicChatClient();
            ChatResponse chatResponse = chatClient.prompt(prompt).call().chatResponse();
            String response = chatResponse.getResult().getOutput().getText();
            
            log.info("动作任务执行完成，动作: {}, 耗时: {}ms", 
                    request.getActionName(),
                    java.time.Duration.between(startTime, LocalDateTime.now()).toMillis());
            
            return TaskResponse.success(
                    response,
                    request.getActionName(),
                    request.getCharacterId(),
                    "action",
                    "task-application-service"
            );
            
        } catch (Exception e) {
            log.error("动作任务执行失败，动作: {}", request.getActionName(), e);
            return TaskResponse.failure(e.getMessage(), request.getActionName());
        }
    }
    
    /**
     * 执行流式任务
     * @param request 任务请求
     * @return 流式任务响应
     */
    public Flux<String> executeTaskStream(TaskRequest request) {
        log.info("开始执行流式动作任务，动作: {}, 角色: {}", request.getActionName(), request.getCharacterId());
        
        try {
            // 1. 获取用户输入内容
            String userInput = getUserInput(request);
            if (userInput == null || userInput.trim().isEmpty()) {
                return Flux.error(new RuntimeException("无法获取输入内容"));
            }

            // 2. 构建系统提示词
            String systemPrompt = buildSystemPrompt(request);

            // 3. 构建消息列表
            List<Message> messages = buildMessages(userInput, systemPrompt);
            
            // 3. 创建Prompt
            Prompt prompt = new Prompt(messages);
            
            // 4. 获取ChatClient并调用流式接口
            ChatClient chatClient = chatClientManager.getBasicChatClient();
            
            return chatClient
                    .prompt(prompt)
                    .stream()
                    .content()
                    .doOnComplete(() -> {
                        log.info("流式动作任务执行完成，动作: {}", request.getActionName());
                    })
                    .doOnError(error -> {
                        log.error("流式动作任务执行失败，动作: {}", request.getActionName(), error);
                    });
            
        } catch (Exception e) {
            log.error("流式动作任务执行异常，动作: {}", request.getActionName(), e);
            return Flux.error(e);
        }
    }
    
    /**
     * 构建系统提示词
     */
    private String buildSystemPrompt(TaskRequest request) {
        StringJoiner promptBuilder = new StringJoiner("\n\n");
        
        // 1. 基础系统提示词
        String basePrompt = systemPromptManager.buildBaseSystemPrompt();
        if (!basePrompt.isEmpty()) {
            promptBuilder.add(basePrompt);
        }
        
        // 2. 角色人格提示词（直接通过角色ID获取）
        String characterPrompt = characterPromptManager.buildCompleteCharacterPrompt(
                request.getCharacterId());
        if (!characterPrompt.isEmpty()) {
            promptBuilder.add(characterPrompt);
        }
        
        // 3. 用户称呼提示词
        String userPrompt = systemPromptManager.buildUserAddressPrompt(request.getUserName());
        if (!userPrompt.isEmpty()) {
            promptBuilder.add(userPrompt);
        }
        
        // 4. 动作行为指导
        String behaviorPrompt = systemPromptManager.buildBehaviorGuidePrompt(
                TemplateType.ACTION_BEHAVIOR);
        if (!behaviorPrompt.isEmpty()) {
            promptBuilder.add(behaviorPrompt);
        }
        
        // 5. 动作特定提示词（根据动作名获取对应模板）
        String actionPrompt = systemPromptManager.buildActionSpecificPrompt(request.getActionName());
        if (!actionPrompt.isEmpty()) {
            promptBuilder.add(actionPrompt);
        }
        
        return promptBuilder.toString();
    }
    
    /**
     * 构建消息列表
     */
    private List<Message> buildMessages(TaskRequest request, String systemPrompt) {
        List<Message> messages = new ArrayList<>();
        
        // 1. 添加系统消息
        if (!systemPrompt.isEmpty()) {
            messages.add(new SystemMessage(systemPrompt));
        }
        
        // 2. 添加当前用户消息（动作任务不使用记忆）
        messages.add(new UserMessage(request.getUserInput()));
        
        return messages;
    }
    
    /**
     * 任务请求DTO
     */
    @lombok.Builder
    @lombok.Data
    public static class TaskRequest {
        private String userInput; // 可选，如果提供则直接使用
        private Long articleId; // 可选，如果提供则根据文章ID获取内容
        private String userName;
        private Boolean isOwner;
        private String characterId;
        private String actionName;
        private String mode; // 保留但不使用，为了API兼容性
    }
    
    /**
     * 任务响应DTO
     */
    @lombok.Builder
    @lombok.Data
    public static class TaskResponse {
        private String response;
        private Boolean success;
        private String errorMessage;
        private String actionName;
        private String characterId;
        private String mode;
        private String strategyName;
        
        public static TaskResponse success(String response, String actionName, 
                String characterId, String mode, String strategyName) {
            return TaskResponse.builder()
                    .response(response)
                    .success(true)
                    .actionName(actionName)
                    .characterId(characterId)
                    .mode(mode)
                    .strategyName(strategyName)
                    .build();
        }
        
        public static TaskResponse failure(String errorMessage, String actionName) {
            return TaskResponse.builder()
                    .success(false)
                    .errorMessage(errorMessage)
                    .actionName(actionName)
                    .build();
        }
    }
}
