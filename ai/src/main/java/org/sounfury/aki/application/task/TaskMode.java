package org.sounfury.aki.application.task;

/**
 * 任务模式枚举
 * 定义无状态任务的类型
 */
public enum TaskMode {
    
    /**
     * 动作模式
     * 特征：挂载角色卡、动作特定模板，不开启记忆和工具
     */
    ACTION("action", "动作模式");
    
    private final String code;
    private final String name;
    
    TaskMode(String code, String name) {
        this.code = code;
        this.name = name;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getName() {
        return name;
    }
    
    /**
     * 根据代码获取模式
     */
    public static TaskMode fromCode(String code) {
        for (TaskMode mode : values()) {
            if (mode.code.equals(code)) {
                return mode;
            }
        }
        throw new IllegalArgumentException("未知的任务模式代码: " + code);
    }
}
