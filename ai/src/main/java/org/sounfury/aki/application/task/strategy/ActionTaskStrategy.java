package org.sounfury.aki.application.task.strategy;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.sounfury.aki.domain.character.CharacterRepository;
import org.sounfury.aki.domain.service.CharacterPromptManager;
import org.sounfury.aki.domain.prompt.template.service.SystemPromptManager;
import org.sounfury.aki.domain.prompt.template.TemplateType;
import org.sounfury.aki.domain.llm.service.ChatClientManager;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.messages.SystemMessage;
import org.springframework.ai.chat.messages.UserMessage;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.StringJoiner;

/**
 * 动作任务策略实现
 * 处理无状态的动作任务，只涉及角色卡，不涉及记忆和工具
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ActionTaskStrategy implements TaskStrategy {

    private final ChatClientManager chatClientManager;
    private final CharacterRepository characterRepository;
    private final SystemPromptManager systemPromptManager;
    private final CharacterPromptManager characterPromptManager;
    
    @Override
    public TaskResult execute(TaskRequest request) {
        LocalDateTime startTime = LocalDateTime.now();
        log.info("开始执行动作任务，动作: {}, 角色: {}", request.actionName(), request.characterId());
        
        try {
            // 1. 构建系统提示词
            String systemPrompt = buildSystemPrompt(request);
            
            // 2. 构建消息列表
            List<Message> messages = buildMessages(request, systemPrompt);
            
            // 3. 创建Prompt
            Prompt prompt = new Prompt(messages);
            
            // 4. 获取ChatClient并调用（不使用工具）
            ChatClient chatClient = chatClientManager.getBasicChatClient();
            ChatResponse chatResponse = chatClient.prompt(prompt).call().chatResponse();
            String response = chatResponse.getResult().getOutput().getText();
            
            log.info("动作任务执行完成，动作: {}, 耗时: {}ms", 
                    request.actionName(),
                    java.time.Duration.between(startTime, LocalDateTime.now()).toMillis());
            
            return TaskResult.success(response, getStrategyName(), request.actionName());
            
        } catch (Exception e) {
            log.error("动作任务执行失败，动作: {}", request.actionName(), e);
            return TaskResult.failure(e.getMessage(), getStrategyName(), request.actionName());
        }
    }
    
    @Override
    public Flux<String> executeStream(TaskRequest request) {
        log.info("开始执行流式动作任务，动作: {}, 角色: {}", request.actionName(), request.characterId());
        
        try {
            // 1. 构建系统提示词
            String systemPrompt = buildSystemPrompt(request);
            
            // 2. 构建消息列表
            List<Message> messages = buildMessages(request, systemPrompt);
            
            // 3. 创建Prompt
            Prompt prompt = new Prompt(messages);
            
            // 4. 获取ChatClient并调用流式接口
            ChatClient chatClient = chatClientManager.getBasicChatClient();
            
            return chatClient
                    .prompt(prompt)
                    .stream()
                    .content()
                    .doOnComplete(() -> {
                        log.info("流式动作任务执行完成，动作: {}", request.actionName());
                    })
                    .doOnError(error -> {
                        log.error("流式动作任务执行失败，动作: {}", request.actionName(), error);
                    });
            
        } catch (Exception e) {
            log.error("流式动作任务执行异常，动作: {}", request.actionName(), e);
            return Flux.error(e);
        }
    }
    
    @Override
    public String getStrategyName() {
        return "action-task-strategy";
    }
    
    /**
     * 构建系统提示词
     */
    private String buildSystemPrompt(TaskRequest request) {
        StringJoiner promptBuilder = new StringJoiner("\n\n");
        
        // 1. 基础系统提示词
        String basePrompt = systemPromptManager.buildBaseSystemPrompt();
        if (!basePrompt.isEmpty()) {
            promptBuilder.add(basePrompt);
        }
        
        // 2. 角色人格提示词（直接通过角色ID获取）
        String characterPrompt = characterPromptManager.buildCompleteCharacterPrompt(
                request.characterId());
        if (!characterPrompt.isEmpty()) {
            promptBuilder.add(characterPrompt);
        }
        
        // 3. 用户称呼提示词
        String userPrompt = systemPromptManager.buildUserAddressPrompt(request.userName());
        if (!userPrompt.isEmpty()) {
            promptBuilder.add(userPrompt);
        }
        
        // 4. 动作行为指导
        String behaviorPrompt = systemPromptManager.buildBehaviorGuidePrompt(
                TemplateType.ACTION_BEHAVIOR);
        if (!behaviorPrompt.isEmpty()) {
            promptBuilder.add(behaviorPrompt);
        }
        
        // 5. 动作特定提示词
        String actionPrompt = systemPromptManager.buildActionSpecificPrompt(request.actionName());
        if (!actionPrompt.isEmpty()) {
            promptBuilder.add(actionPrompt);
        }
        
        return promptBuilder.toString();
    }
    
    /**
     * 构建消息列表
     */
    private List<Message> buildMessages(TaskRequest request, String systemPrompt) {
        List<Message> messages = new ArrayList<>();
        
        // 1. 添加系统消息
        if (!systemPrompt.isEmpty()) {
            messages.add(new SystemMessage(systemPrompt));
        }
        
        // 2. 添加当前用户消息（动作任务不使用记忆）
        messages.add(new UserMessage(request.userInput()));
        
        return messages;
    }
}
