package org.sounfury.aki.application.task.strategy;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.sounfury.aki.application.task.dto.BaseTaskRequest;
import org.sounfury.aki.application.task.dto.CompanionTaskRequest;
import org.sounfury.aki.domain.llm.service.ChatClientManager;
import org.sounfury.aki.domain.prompt.service.CharacterPromptManager;
import org.sounfury.aki.domain.prompt.service.SystemPromptManager;
import org.sounfury.aki.domain.prompt.template.TemplateType;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.messages.SystemMessage;
import org.springframework.ai.chat.messages.UserMessage;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.StringJoiner;

/**
 * 陪伴任务策略
 * 处理发布祝贺、登录欢迎等任务
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class CompanionTaskStrategy implements TaskStrategy {
    
    private final ChatClientManager chatClientManager;
    private final SystemPromptManager systemPromptManager;
    private final CharacterPromptManager characterPromptManager;
    
    @Override
    public TaskResult execute(BaseTaskRequest request) {
        if (!(request instanceof CompanionTaskRequest companionRequest)) {
            return TaskResult.failure("请求类型错误，期望CompanionTaskRequest", "CompanionTaskStrategy");
        }
        
        LocalDateTime startTime = LocalDateTime.now();
        log.info("开始执行陪伴任务，任务模式: {}, 事件类型: {}", 
                companionRequest.getTaskMode(), companionRequest.getEventType());
        
        try {
            // 1. 构建系统提示词
            String systemPrompt = buildSystemPrompt(companionRequest);
            
            // 2. 构建消息列表
            List<Message> messages = buildMessages(companionRequest.getContextInfo(), systemPrompt);
            
            // 3. 创建Prompt
            Prompt prompt = new Prompt(messages);
            
            // 4. 获取ChatClient并调用
            ChatClient chatClient = chatClientManager.getBasicChatClient();
            ChatResponse chatResponse = chatClient.prompt(prompt).call().chatResponse();
            String response = chatResponse.getResult().getOutput().getText();
            
            log.info("陪伴任务执行完成，任务模式: {}, 耗时: {}ms", 
                    companionRequest.getTaskMode(),
                    java.time.Duration.between(startTime, LocalDateTime.now()).toMillis());
            
            return TaskResult.success(response, "CompanionTaskStrategy");
            
        } catch (Exception e) {
            log.error("陪伴任务执行失败，任务模式: {}", companionRequest.getTaskMode(), e);
            return TaskResult.failure(e.getMessage(), "CompanionTaskStrategy");
        }
    }
    
    @Override
    public Flux<String> executeStream(BaseTaskRequest request) {
        if (!(request instanceof CompanionTaskRequest companionRequest)) {
            return Flux.error(new IllegalArgumentException("请求类型错误，期望CompanionTaskRequest"));
        }
        
        log.info("开始执行流式陪伴任务，任务模式: {}, 事件类型: {}", 
                companionRequest.getTaskMode(), companionRequest.getEventType());
        
        try {
            // 1. 构建系统提示词
            String systemPrompt = buildSystemPrompt(companionRequest);
            
            // 2. 构建消息列表
            List<Message> messages = buildMessages(companionRequest.getContextInfo(), systemPrompt);
            
            // 3. 创建Prompt
            Prompt prompt = new Prompt(messages);
            
            // 4. 获取ChatClient并调用流式接口
            ChatClient chatClient = chatClientManager.getBasicChatClient();
            
            return chatClient
                    .prompt(prompt)
                    .stream()
                    .content()
                    .doOnComplete(() -> {
                        log.info("流式陪伴任务执行完成，任务模式: {}", companionRequest.getTaskMode());
                    })
                    .doOnError(error -> {
                        log.error("流式陪伴任务执行失败，任务模式: {}", companionRequest.getTaskMode(), error);
                    });
            
        } catch (Exception e) {
            log.error("流式陪伴任务执行异常，任务模式: {}", companionRequest.getTaskMode(), e);
            return Flux.error(e);
        }
    }
    
    /**
     * 构建系统提示词
     */
    private String buildSystemPrompt(CompanionTaskRequest request) {
        StringJoiner promptBuilder = new StringJoiner("\n\n");
        
        // 1. 基础系统提示词
        String basePrompt = systemPromptManager.buildBaseSystemPrompt();
        if (!basePrompt.isEmpty()) {
            promptBuilder.add(basePrompt);
        }
        
        // 2. 角色人格提示词（使用默认角色）
        String characterPrompt = characterPromptManager.buildCompleteCharacterPrompt("default");
        if (!characterPrompt.isEmpty()) {
            promptBuilder.add(characterPrompt);
        }
        
        // 3. 用户称呼提示词
        String userPrompt = systemPromptManager.buildUserAddressPrompt(request.getUserName());
        if (!userPrompt.isEmpty()) {
            promptBuilder.add(userPrompt);
        }
        
        // 4. 陪伴行为指导
        String behaviorPrompt = systemPromptManager.buildBehaviorGuidePrompt(TemplateType.COMPANION_BEHAVIOR);
        if (!behaviorPrompt.isEmpty()) {
            promptBuilder.add(behaviorPrompt);
        }
        
        // 5. 任务特定提示词（根据任务模式获取对应模板）
        String taskPrompt = systemPromptManager.buildTaskSpecificPrompt(request.getTaskMode().getCode());
        if (!taskPrompt.isEmpty()) {
            promptBuilder.add(taskPrompt);
        }
        
        return promptBuilder.toString();
    }
    
    /**
     * 构建消息列表
     */
    private List<Message> buildMessages(String contextInfo, String systemPrompt) {
        List<Message> messages = new ArrayList<>();
        
        // 1. 添加系统消息
        if (!systemPrompt.isEmpty()) {
            messages.add(new SystemMessage(systemPrompt));
        }
        
        // 2. 添加上下文信息作为用户消息
        String userMessage = contextInfo != null && !contextInfo.trim().isEmpty() 
                ? contextInfo 
                : "请根据当前场景生成合适的陪伴话语";
        messages.add(new UserMessage(userMessage));
        
        return messages;
    }
}
