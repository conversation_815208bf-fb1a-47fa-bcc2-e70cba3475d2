package org.sounfury.aki.domain.prompt.template.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.sounfury.aki.domain.prompt.template.PromptTemplateRepository;
import org.sounfury.aki.domain.prompt.template.TemplateType;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * 系统提示词服务
 * 领域层的系统提示词服务，调用Repository接口
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SystemPromptManager {

    private final PromptTemplateRepository templateRepository;

    /**
     * 构建基础系统提示词
     * @return 系统基础提示词
     */
    public String buildBaseSystemPrompt() {
        return templateRepository.findByTypeAndEnabled(TemplateType.SYSTEM_PROMPT, true)
                .stream()
                .findFirst()
                .map(template -> {
                    String rendered = template.render(Map.of());
                    log.debug("构建基础系统提示词: {}", template.getName());
                    return rendered;
                })
                .orElse("");
    }

    /**
     * 构建用户称呼提示词
     * @param userName 用户名称
     * @return 用户称呼提示词
     */
    public String buildUserAddressPrompt(String userName) {
        if (userName == null || userName.trim().isEmpty()) {
            return "";
        }

        return templateRepository.findByTypeAndEnabled(TemplateType.USER_ADDRESS, true)
                .stream()
                .findFirst()
                .map(template -> {
                    Map<String, String> variables = Map.of("user_name", userName.trim());
                    String rendered = template.render(variables);
                    log.debug("构建用户称呼提示词: {}", userName);
                    return rendered;
                })
                .orElse("");
    }

    /**
     * 构建行为指导提示词
     * @param behaviorType 行为指导类型
     * @return 行为指导提示词
     */
    public String buildBehaviorGuidePrompt(TemplateType behaviorType) {
        return templateRepository.findByTypeAndEnabled(behaviorType, true)
                .stream()
                .findFirst()
                .map(template -> {
                    String rendered = template.render(Map.of());
                    log.debug("构建行为指导提示词: {}", behaviorType.getName());
                    return rendered;
                })
                .orElse("");
    }

    /**
     * 构建动作特定提示词
     * @param actionName 动作名称
     * @return 动作特定提示词
     */
    public String
    buildActionSpecificPrompt(String actionName) {
        TemplateType actionTemplateType = getActionTemplateType(actionName);
        if (actionTemplateType == null) {
            log.debug("未找到动作 {} 对应的特定模板", actionName);
            return "";
        }

        return templateRepository.findByTypeAndEnabled(actionTemplateType, true)
                .stream()
                .findFirst()
                .map(template -> {
                    String rendered = template.render(Map.of());
                    log.debug("构建动作特定提示词: {} -> {}", actionName, actionTemplateType.getName());
                    return rendered;
                })
                .orElse("");
    }

    /**
     * 根据动作名称获取对应的模板类型
     * @param actionName 动作名称
     * @return 对应的模板类型，如果不存在则返回null
     */
    public TemplateType getActionTemplateType(String actionName) {
        if (actionName == null || actionName.trim().isEmpty()) {
            return null;
        }

        return switch (actionName.toLowerCase().trim()) {
            case "summary", "summarize", "总结" -> TemplateType.ACTION_SUMMARY;
            case "translation", "translate", "翻译" -> TemplateType.ACTION_TRANSLATION;
            case "code", "code_generation", "代码生成" -> TemplateType.ACTION_CODE_GENERATION;
            default -> null;
        };
    }
}
