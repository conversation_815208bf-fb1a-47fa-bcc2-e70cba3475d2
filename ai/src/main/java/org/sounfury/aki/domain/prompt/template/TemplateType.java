package org.sounfury.aki.domain.prompt.template;

/**
 * 提示词模板类型枚举
 * 定义不同类型的提示词模板
 */
public enum TemplateType {
    
    /**
     * 系统提示词模板
     * 例如："你是世界一流的演员"
     */
    SYSTEM_PROMPT("system_prompt", "系统提示词", "定义AI的基础角色和能力"),
    
    /**
     * 性格描述模板
     * 例如："你的性格是{personality}"
     */
    PERSONALITY_PROMPT("personality_prompt", "性格描述", "描述角色的性格特征"),
    
    /**
     * 场景设定模板
     * 例如："当前场景是{scenario}"
     */
    SCENARIO_PROMPT("scenario_prompt", "场景设定", "描述当前的世界设定和场景"),
    
    /**
     * 示例对话模板
     * 例如："以下是示例对话：{examples}"
     */
    EXAMPLE_DIALOGUE("example_dialogue", "示例对话", "提供对话示例"),
    
    /**
     * 角色介绍模板
     * 例如："你扮演的角色是{character_name}"
     */
    CHARACTER_INTRO("character_intro", "角色介绍", "介绍角色的基本信息"),
    
    /**
     * 用户称呼模板
     * 例如："用户的名字是{user_name}，你可以称呼他为{user_name}"
     */
    USER_ADDRESS("user_address", "用户称呼", "定义如何称呼用户"),
    
    /**
     * 通用行为指导模板
     * 例如："请保持角色一致性，不要跳出角色设定"
     */
    BEHAVIOR_GUIDE("behavior_guide", "通用行为指导", "指导AI的通用行为规范"),

    /**
     * 对话行为指导模板
     * 针对普通对话场景的行为指导
     */
    CONVERSATION_BEHAVIOR("conversation_behavior", "对话行为指导", "指导AI在对话中的行为规范"),

    /**
     * 动作行为指导模板
     * 针对特定任务执行的行为指导
     */
    ACTION_BEHAVIOR("action_behavior", "动作行为指导", "指导AI在执行特定任务时的行为规范"),

    /**
     * Agent行为指导模板
     * 针对工具调用场景的行为指导
     */
    AGENT_BEHAVIOR("agent_behavior", "Agent行为指导", "指导AI在使用工具时的行为规范"),

    /**
     * 总结动作模板
     * 专门用于文章总结任务
     */
    ACTION_SUMMARY("action_summary", "总结动作", "指导AI进行文章总结的专用模板");


    private final String code;
    private final String name;
    private final String description;
    
    TemplateType(String code, String name, String description) {
        this.code = code;
        this.name = name;
        this.description = description;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getName() {
        return name;
    }
    
    public String getDescription() {
        return description;
    }
    
    /**
     * 根据代码获取模板类型
     */
    public static TemplateType fromCode(String code) {
        for (TemplateType type : values()) {
            if (type.code.equals(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("未知的模板类型代码: " + code);
    }
}
