package org.sounfury.aki.api;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.sounfury.aki.application.task.service.TaskApplicationService;
import org.sounfury.core.convention.exception.ClientException;
import org.sounfury.core.convention.result.Result;
import org.sounfury.core.convention.result.Results;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;
import org.springframework.http.MediaType;

@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/ai/action")
public class TaskController {

    private final TaskApplicationService taskApplicationService;

    /**
     * 总结指定文章
     * @param articleId 文章ID
     * @return 总结结果
     */
    @PostMapping("/summarize/article/{articleId}")
    public Result<TaskApplicationService.TaskResponse> summarizeArticle(@PathVariable Long articleId) {
        try {
            log.info("总结文章请求，文章ID: {}", articleId);

            // 构建任务请求
            TaskApplicationService.TaskRequest request = TaskApplicationService.TaskRequest.builder()
                    .userInput("请总结文章ID为 " + articleId + " 的文章内容")
                    .userName("系统")
                    .isOwner(true)
                    .characterId("default")
                    .actionName("summary")
                    .mode("action")
                    .build();

            // 执行任务
            TaskApplicationService.TaskResponse response = taskApplicationService.executeTask(request);

            return Results.success(response);
        } catch (Exception e) {
            log.error("总结文章异常，文章ID: {}", articleId, e);
            throw new ClientException("服务异常");
        }
    }

    /**
     * 总结指定文章（流式输出）
     * @param articleId 文章ID
     * @return 流式总结结果
     */
    @GetMapping(value = "/summarize/article/{articleId}/stream", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<String> summarizeArticleStream(@PathVariable Long articleId) {
        try {
            log.info("流式总结文章请求，文章ID: {}", articleId);

            // 构建任务请求
            TaskApplicationService.TaskRequest request = TaskApplicationService.TaskRequest.builder()
                    .userInput("请总结文章ID为 " + articleId + " 的文章内容")
                    .userName("系统")
                    .isOwner(true)
                    .characterId("default")
                    .actionName("summary")
                    .mode("action")
                    .build();

            // 执行流式任务
            return taskApplicationService.executeTaskStream(request);
        } catch (Exception e) {
            log.error("流式总结文章异常，文章ID: {}", articleId, e);
            return Flux.error(new ClientException("服务异常"));
        }
    }
}
